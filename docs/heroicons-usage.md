# 🎨 Heroicons 使用指南

## 概述

本项目已完全集成 Heroicons 图标库，所有 1288 个图标都已预编译到 CSS 中，确保不会被 Tailwind CSS 的 purge 机制移除。

## 📊 图标统计

- **总图标数**: 1288
- **Outline 图标**: 324 (24px)
- **Solid 图标**: 324 (24px) 
- **Mini 图标**: 324 (20px)
- **Micro 图标**: 316 (16px)

## 🚀 使用方法

### 基本语法

```html
<div class="hero-{图标名}{变体} w-{尺寸} h-{尺寸}"></div>
```

### 图标变体

| 变体 | 后缀 | 尺寸 | 描述 |
|------|------|------|------|
| Outline | 无 | 24px | 线条风格，默认 1.5rem |
| Solid | `-solid` | 24px | 填充风格，默认 1.5rem |
| Mini | `-mini` | 20px | 小尺寸填充，默认 1.25rem |
| Micro | `-micro` | 16px | 微小尺寸填充，默认 1rem |

### 使用示例

```html
<!-- Outline 图标 (默认) -->
<div class="hero-users w-6 h-6"></div>
<div class="hero-home w-6 h-6"></div>
<div class="hero-envelope w-6 h-6"></div>

<!-- Solid 图标 -->
<div class="hero-users-solid w-6 h-6"></div>
<div class="hero-home-solid w-6 h-6"></div>
<div class="hero-envelope-solid w-6 h-6"></div>

<!-- Mini 图标 -->
<div class="hero-users-mini w-5 h-5"></div>
<div class="hero-home-mini w-5 h-5"></div>
<div class="hero-envelope-mini w-5 h-5"></div>

<!-- Micro 图标 -->
<div class="hero-users-micro w-4 h-4"></div>
<div class="hero-home-micro w-4 h-4"></div>
<div class="hero-envelope-micro w-4 h-4"></div>
```

### 自定义样式

```html
<!-- 自定义颜色 -->
<div class="hero-heart w-6 h-6 text-red-500"></div>
<div class="hero-star w-6 h-6 text-yellow-400"></div>

<!-- 自定义尺寸 -->
<div class="hero-home w-8 h-8"></div>
<div class="hero-home w-12 h-12"></div>

<!-- 组合使用 -->
<div class="hero-bell w-6 h-6 text-blue-500 hover:text-blue-700 transition-colors"></div>
```

## 🔧 技术实现

### 文件结构

```
assets/
├── css/
│   ├── app.css                    # 主 CSS 文件
│   └── heroicons-force.css        # 所有图标的 CSS 定义
├── vendor/
│   └── heroicons.js              # Tailwind 插件（简化版）
└── heroicons-safelist.js         # Tailwind safelist 配置

scripts/
└── generate_heroicons_css.js     # 图标 CSS 生成脚本
```

### 自动生成

所有图标的 CSS 定义都是通过 `scripts/generate_heroicons_css.js` 脚本自动生成的：

```bash
# 重新生成所有图标 CSS
node scripts/generate_heroicons_css.js
```

### 构建流程

1. **生成 CSS**: 脚本读取 SVG 文件并生成对应的 CSS 类
2. **编译**: `mix assets.build` 将所有 CSS 编译到最终文件
3. **验证**: 插件统计并验证图标数量

## 📝 常用图标

### 用户相关
- `hero-user` - 单个用户
- `hero-users` - 多个用户
- `hero-user-group` - 用户组
- `hero-user-circle` - 圆形用户头像
- `hero-user-plus` - 添加用户
- `hero-user-minus` - 删除用户

### 导航相关
- `hero-home` - 首页
- `hero-bars-3` - 菜单
- `hero-x-mark` - 关闭
- `hero-chevron-left` - 左箭头
- `hero-chevron-right` - 右箭头
- `hero-chevron-up` - 上箭头
- `hero-chevron-down` - 下箭头

### 操作相关
- `hero-plus` - 添加
- `hero-minus` - 删除
- `hero-pencil` - 编辑
- `hero-trash` - 删除
- `hero-eye` - 查看
- `hero-eye-slash` - 隐藏

### 通信相关
- `hero-envelope` - 邮件
- `hero-phone` - 电话
- `hero-chat-bubble-left` - 聊天
- `hero-bell` - 通知
- `hero-megaphone` - 公告

## 🎯 最佳实践

### 1. 尺寸一致性
```html
<!-- 推荐：使用标准尺寸 -->
<div class="hero-home w-4 h-4"></div>  <!-- 16px -->
<div class="hero-home w-5 h-5"></div>  <!-- 20px -->
<div class="hero-home w-6 h-6"></div>  <!-- 24px -->
<div class="hero-home w-8 h-8"></div>  <!-- 32px -->
```

### 2. 语义化使用
```html
<!-- 推荐：根据功能选择合适的变体 -->
<button class="hero-plus w-5 h-5">添加</button>           <!-- outline 用于按钮 -->
<div class="hero-check-solid w-4 h-4 text-green-500"></div> <!-- solid 用于状态 -->
```

### 3. 颜色搭配
```html
<!-- 推荐：使用语义化颜色 -->
<div class="hero-exclamation-triangle w-5 h-5 text-yellow-500"></div>  <!-- 警告 -->
<div class="hero-check-circle w-5 h-5 text-green-500"></div>           <!-- 成功 -->
<div class="hero-x-circle w-5 h-5 text-red-500"></div>                <!-- 错误 -->
<div class="hero-information-circle w-5 h-5 text-blue-500"></div>      <!-- 信息 -->
```

## 🔄 更新图标

如果需要更新图标库：

1. 更新 `deps/heroicons` 依赖
2. 运行生成脚本：`node scripts/generate_heroicons_css.js`
3. 重新构建：`mix assets.build`

## ✅ 优势

- ✅ **无 Purge 问题**: 所有图标都预编译，不会被移除
- ✅ **完整覆盖**: 包含所有 1288 个图标
- ✅ **自动化**: 脚本自动生成，易于维护
- ✅ **类型安全**: 所有图标类都在 CSS 中定义
- ✅ **性能优化**: 使用 CSS mask 技术，支持颜色自定义
- ✅ **响应式**: 支持任意尺寸调整

## 🐛 故障排除

### 图标不显示
1. 检查类名是否正确
2. 确保设置了 `w-*` 和 `h-*` 类
3. 检查是否有 `text-*` 颜色类

### 构建失败
1. 检查 `deps/heroicons` 目录是否存在
2. 运行 `node scripts/generate_heroicons_css.js` 重新生成
3. 检查 `assets/css/heroicons-force.css` 是否存在

### 新图标不可用
1. 确认图标在 heroicons 库中存在
2. 重新运行生成脚本
3. 重新构建项目
