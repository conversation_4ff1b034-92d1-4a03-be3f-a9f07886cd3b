services:
  app:
    build:
      context: .
      target: builder
      args:
        MIX_ENV: dev
    env_file: demo/.env
    environment:
      - STARTUP_COMMAND_1=mix do deps.get, ecto.create, ecto.migrate
      - STARTUP_COMMAND_2=yarn install --pure-lockfile
      - STARTUP_COMMAND_3=mix assets.setup
    volumes:
      - .:/opt/app
    ports:
      - 4000:4000
    depends_on:
      postgres:
        condition: service_healthy
  postgres:
    image: postgres:16.9@sha256:2e7c735993bf456ee1977c40dd82e66875e25f7ee9dfe1e5118fb24887104d85
    environment:
      - POSTGRES_PASSWORD=postgres
    ports:
      - 54321:5432
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
