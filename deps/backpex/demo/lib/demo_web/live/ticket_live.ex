defmodule DemoWeb.TicketLive do
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Demo.Helpdesk.Ticket
    ],
    layout: {DemoWeb.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "Ticket"

  @impl Backpex.LiveResource
  def plural_name, do: "Tickets"

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :index, :before_page_title) do
    ~H"""
    <Backpex.HTML.Layout.alert kind={:info} closable={false}>
      This resource uses the <strong>Ash adapter</strong> with form support for create and edit operations.
      Testing the new form functionality for Ash resources.
    </Backpex.HTML.Layout.alert>
    """
  end

  @impl Backpex.LiveResource
  def can?(_assigns, action, _item) when action in [:index, :show, :delete, :new, :edit], do: true
  def can?(_assigns, _action, _item), do: false

  @impl Backpex.LiveResource
  def fields do
    [
      subject: %{
        module: Backpex.Fields.Text,
        label: "Subject",
        orderable: false
      },
      body: %{
        module: Backpex.Fields.Textarea,
        label: "Body",
        orderable: false,
        except: [:index]
      }
    ]
  end
end
