defmodule Backpex.Fields.BelongsTo do
  @config_schema [
    display_field: [
      doc: "The field of the relation to be used for searching, ordering and displaying values.",
      type: :atom,
      required: true
    ],
    display_field_form: [
      doc: "Field to be used to display form values.",
      type: :atom
    ],
    live_resource: [
      doc: "The live resource of the association. Used to generate links navigating to the association.",
      type: :atom
    ],
    options_query: [
      doc: """
      Manipulates the list of available options in the select.

      Defaults to `fn (query, _field) -> query end` which returns all entries.
      """,
      type: {:fun, 2}
    ],
    prompt: [
      doc: "The text to be displayed when no option is selected or function that receives the assigns.",
      type: {:or, [:string, {:fun, 1}]}
    ],
    debounce: [
      doc: "Timeout value (in milliseconds), \"blur\" or function that receives the assigns.",
      type: {:or, [:pos_integer, :string, {:fun, 1}]}
    ],
    throttle: [
      doc: "Timeout value (in milliseconds) or function that receives the assigns.",
      type: {:or, [:pos_integer, {:fun, 1}]}
    ]
  ]

  @moduledoc """
  A field for handling a `belongs_to` relation.

  ## Field-specific options

  See `Backpex.Field` for general field options.

  #{NimbleOptions.docs(@config_schema)}

  ## Example

      @impl Backpex.LiveResource
      def fields do
      [
        user: %{
          module: Backpex.Fields.BelongsTo,
          label: "Username",
          display_field: :username,
          options_query: &where(&1, [user], user.role == :admin),
          live_resource: DemoWeb.UserLive
        }
      ]
      end
  """
  use Backpex.Field, config_schema: @config_schema
  import Ecto.Query
  alias Backpex.Router

  @impl Phoenix.LiveComponent
  def update(assigns, socket) do
    %{name: name, field: field} = assigns
    adapter_config = assigns.live_resource.config(:adapter_config)

    # Extract field_options from the field tuple
    {_field_name, field_options} = field

    {queryable, owner_key} =
      cond do
        # 检查是否有 resource 配置（Ash 模式）
        Keyword.has_key?(adapter_config, :resource) ->
          # Ash mode - use the configured resource
          resource = adapter_config[:resource]
          target_resource = get_ash_association_resource(resource, name)
          {target_resource, name}

        # 检查 schema 是否是 Ash 资源
        is_ash_resource?(adapter_config[:schema]) ->
          # Ash mode - determine resource from schema association
          resource = get_ash_association_resource(adapter_config[:schema], name)
          {resource, name}

        true ->
          # Ecto mode - use schema associations
          %{queryable: queryable, owner_key: owner_key} = adapter_config[:schema].__schema__(:association, name)
          {queryable, owner_key}
      end

    display_field = display_field(field)
    display_field_form = display_field_form(field, display_field)

    socket
    |> assign(assigns)
    |> assign(queryable: queryable)
    |> assign(owner_key: owner_key)
    |> assign(display_field: display_field)
    |> assign(display_field_form: display_field_form)
    |> ok()
  end

  @impl Backpex.Field
  def render_value(%{value: value} = assigns) when is_nil(value) do
    ~H"""
    <p class={@live_action in [:index, :resource_action] && "truncate"}>
      {HTML.pretty_value(nil)}
    </p>
    """
  end

  @impl Backpex.Field
  def render_value(assigns) do
    %{value: value, display_field: display_field} = assigns

    assigns =
      assigns
      |> assign(:display_text, Map.get(value, display_field))
      |> assign_link()

    ~H"""
    <div class={[@live_action in [:index, :resource_action] && "truncate"]}>
      <%= if @link do %>
        <.link navigate={@link} class={[@live_action in [:index, :resource_action] && "truncate", "hover:underline"]}>
          {@display_text}
        </.link>
      <% else %>
        <p class={@live_action in [:index, :resource_action] && "truncate"}>
          {HTML.pretty_value(@display_text)}
        </p>
      <% end %>
    </div>
    """
  end

  @impl Backpex.Field
  def render_form(assigns) do
    %{
      field_options: field_options,
      queryable: queryable,
      owner_key: owner_key,
      display_field_form: display_field_form
    } = assigns

    adapter_config = assigns.live_resource.config(:adapter_config)
    options = get_options(adapter_config, queryable, field_options, display_field_form, assigns)

    assigns =
      assigns
      |> assign(:options, options)
      |> assign(:owner_key, owner_key)
      |> assign_prompt(field_options)

    ~H"""
    <div>
      <Layout.field_container>
        <:label align={Backpex.Field.align_label(@field_options, assigns)}>
          <Layout.input_label text={@field_options[:label]} />
        </:label>
        <BackpexForm.input
          type="select"
          field={@form[@owner_key]}
          options={@options}
          prompt={@prompt}
          translate_error_fun={Backpex.Field.translate_error_fun(@field_options, assigns)}
          help_text={Backpex.Field.help_text(@field_options, assigns)}
          phx-debounce={Backpex.Field.debounce(@field_options, assigns)}
          phx-throttle={Backpex.Field.throttle(@field_options, assigns)}
        />
      </Layout.field_container>
    </div>
    """
  end

  @impl Backpex.Field
  def render_index_form(assigns) do
    %{field_options: field_options, queryable: queryable, display_field_form: display_field_form} = assigns
    adapter_config = assigns.live_resource.config(:adapter_config)
    options = get_options(adapter_config, queryable, field_options, display_field_form, assigns)
    form = to_form(%{"value" => assigns.value}, as: :index_form)

    assigns =
      assigns
      |> assign(:options, options)
      |> assign_new(:form, fn -> form end)
      |> assign_new(:valid, fn -> true end)
      |> assign_prompt(assigns.field_options)

    ~H"""
    <div>
      <.form for={@form} class="relative" phx-change="update-field" phx-submit="update-field" phx-target={@myself}>
        <BackpexForm.input
          id={"index-form-input-#{@name}-#{LiveResource.primary_value(@item, @live_resource)}"}
          type="select"
          field={@form[:value]}
          options={@options}
          prompt={@prompt}
          value={@value && Map.get(@value, :id)}
          input_class={[
            "select select-sm",
            @valid && "not-hover:select-ghost",
            !@valid && "select-error text-error-content bg-error/10"
          ]}
          disabled={@readonly}
          hide_errors
        />
      </.form>
    </div>
    """
  end

  @impl Phoenix.LiveComponent
  def handle_event("update-field", %{"index_form" => %{"value" => value}}, socket) do
    Backpex.Field.handle_index_editable(socket, value, Map.put(%{}, socket.assigns.owner_key, value))
  end

  @impl Backpex.Field
  def display_field({_name, field_options}) do
    Map.get(field_options, :display_field)
  end

  @impl Backpex.Field
  def schema({name, field_options}, schema) do
    # 注意：在 Ash 模式下，schema 参数可能是 nil，因为 Ash 适配器的 adapter_config 只有 resource 字段
    # 我们需要通过其他方式来判断是否是 Ash 模式
    # 这里我们简单地检查 schema 是否为 nil 或者是否是 Ash 资源
    cond do
      # 如果 schema 是 nil，可能是 Ash 模式，但我们无法确定目标资源
      # 在这种情况下，我们返回一个占位符，实际的资源会在 update/2 中确定
      is_nil(schema) ->
        # 返回一个占位符，实际逻辑在 update/2 中处理
        :ash_placeholder

      # 如果 schema 是 Ash 资源
      is_ash_resource?(schema) ->
        # Ash mode - get resource from schema association
        get_ash_association_resource(schema, name)

      # Ecto 模式
      true ->
        # Ecto mode - use schema associations
        schema.__schema__(:association, name)
        |> Map.get(:queryable)
    end
  end

  @impl Backpex.Field
  def association?(_field), do: true

  defp display_field_form({_name, field_options} = _field, display_field) do
    Map.get(field_options, :display_field_form, display_field)
  end

  defp get_options(adapter_config, queryable, field_options, display_field, assigns) do
    case adapter_config[:repo] do
      nil ->
        # Ash mode - use Ash.read!
        queryable
        |> maybe_options_query_ash(field_options, assigns)
        |> Ash.read!()
        |> Enum.map(&{Map.get(&1, display_field), Map.get(&1, :id)})

      repo ->
        # Ecto mode - use repo.all
        queryable
        |> from()
        |> maybe_options_query(field_options, assigns)
        |> repo.all()
        |> Enum.map(&{Map.get(&1, display_field), Map.get(&1, :id)})
    end
  end

  defp assign_link(assigns) do
    %{socket: socket, field_options: field_options, value: value, live_resource: live_resource, params: params} =
      assigns

    link =
      if Map.has_key?(field_options, :live_resource) and live_resource.can?(assigns, :show, value) do
        Router.get_path(socket, Map.get(field_options, :live_resource), params, :show, value)
      else
        nil
      end

    assign(assigns, :link, link)
  end

  defp maybe_options_query(query, %{options_query: options_query} = _field_options, assigns),
    do: options_query.(query, assigns)

  defp maybe_options_query(query, _field_options, _assigns), do: query

  defp maybe_options_query_ash(resource, %{options_query: options_query} = _field_options, assigns) do
    # For Ash mode, we need to handle options_query differently
    # Since options_query expects an Ecto query, we'll skip it for now
    # TODO: Implement proper Ash query filtering
    resource
  end

  defp maybe_options_query_ash(resource, _field_options, _assigns), do: resource

  # 检测是否是 Ash 资源
  defp is_ash_resource?(schema) do
    Code.ensure_loaded?(schema) and Ash.Resource.Info.resource?(schema)
  end

  # 从 Ash 资源中获取关联资源
  defp get_ash_association_resource(schema, association_name) do
    try do
      # 尝试从 Ash 资源的关联中获取目标资源
      case Ash.Resource.Info.relationship(schema, association_name) do
        %{destination: destination} -> destination
        _ -> schema  # 如果找不到关联，返回原始 schema
      end
    rescue
      _ -> schema  # 如果出错，返回原始 schema
    end
  end

  defp assign_prompt(assigns, field_options) do
    prompt =
      case Map.get(field_options, :prompt) do
        nil -> nil
        prompt when is_function(prompt) -> prompt.(assigns)
        prompt -> prompt
      end

    assign(assigns, :prompt, prompt)
  end
end
