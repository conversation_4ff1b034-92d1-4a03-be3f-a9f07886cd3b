/** @type {import('tailwindcss').Config} */
module.exports = {
  content: {
    files: [
      // Phoenix 模板文件
      "./lib/**/*.{ex,exs,heex}",
      "./lib/**/components/**/*.{ex,exs,heex}",
      "./lib/**/live/**/*.{ex,exs,heex}",
      "./lib/**/layouts/**/*.{ex,exs,heex}",

      // JavaScript 文件
      "./assets/js/**/*.js",

      // CSS 文件（包含图标安全列表）
      "./assets/css/**/*.css",

      // Backpex 相关文件
      "./deps/backpex/**/*.{ex,exs,heex}",
      "./local_deps/backpex/**/*.{ex,exs,heex}",

      // 其他可能包含类名的文件
      "./priv/**/*.{html,heex}",

      // 测试文件
      "./*.html",
    ],
    // 自定义提取器，确保提取所有 hero- 开头的类
    extract: {
      heex: (content) => {
        // 提取所有可能的 hero- 类名
        const heroClasses = content.match(/hero-[a-z0-9-]+(?:-(?:solid|mini|micro))?/g) || [];
        // 提取其他标准类名
        const standardClasses = content.match(/[a-zA-Z0-9_-]+/g) || [];
        return [...heroClasses, ...standardClasses];
      },
      ex: (content) => {
        // 对 Elixir 文件进行特殊处理
        const heroClasses = content.match(/hero-[a-z0-9-]+(?:-(?:solid|mini|micro))?/g) || [];
        const standardClasses = content.match(/[a-zA-Z0-9_-]+/g) || [];
        return [...heroClasses, ...standardClasses];
      }
    }
  },
  
  theme: {
    extend: {
      // 扩展主题配置
    }
  },
  
  plugins: [
    // DaisyUI 插件
    require("daisyui"),

    // Heroicons 插件
    require("./assets/vendor/heroicons.js"),
  ],
  
  // DaisyUI 配置
  daisyui: {
    themes: ["light", "dark", "cupcake"],
    darkTheme: "dark",
    base: true,
    styled: true,
    utils: true,
    prefix: "",
    logs: true,
    themeRoot: ":root",
  },
  
  // 确保所有 hero- 类都被包含
  safelist: [

    // 强制包含所有 hero- 图标类模式
    {
      pattern: /^hero-[a-z0-9-]+$/,
      variants: ['hover', 'focus', 'active', 'group-hover'],
    },
    {
      pattern: /^hero-[a-z0-9-]+-solid$/,
      variants: ['hover', 'focus', 'active', 'group-hover'],
    },
    {
      pattern: /^hero-[a-z0-9-]+-mini$/,
      variants: ['hover', 'focus', 'active', 'group-hover'],
    },
    {
      pattern: /^hero-[a-z0-9-]+-micro$/,
      variants: ['hover', 'focus', 'active', 'group-hover'],
    },
  ],
}
