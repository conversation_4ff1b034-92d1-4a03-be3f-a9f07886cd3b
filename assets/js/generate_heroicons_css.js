#!/usr/bin/env node

/**
 * 生成所有 Heroicons 图标的 CSS 定义
 * 这个脚本会读取所有 SVG 文件并生成对应的 CSS 类
 */

const fs = require('fs');
const path = require('path');

// 图标目录配置
const iconsDir = path.join(__dirname, '../../deps/heroicons/optimized');
const outputFile = path.join(__dirname, '../css/heroicons-force.css');

// 图标变体配置
const variants = [
  { suffix: '', dir: '/24/outline', size: '1.5rem' },
  { suffix: '-solid', dir: '/24/solid', size: '1.5rem' },
  { suffix: '-mini', dir: '/20/solid', size: '1.25rem' },
  { suffix: '-micro', dir: '/16/solid', size: '1rem' }
];

/**
 * URL 编码 SVG 内容
 */
function encodeSvgContent(content) {
  return encodeURIComponent(content.replace(/\r?\n|\r/g, ''));
}

/**
 * 生成单个图标的 CSS 类
 */
function generateIconCSS(name, svgContent, size) {
  const encodedContent = encodeSvgContent(svgContent);
  return `.hero-${name} {
  --hero-${name}: url('data:image/svg+xml;utf8,${encodedContent}');
  -webkit-mask: var(--hero-${name});
  mask: var(--hero-${name});
  mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: ${size};
  height: ${size};
}`;
}

/**
 * 处理单个图标变体目录
 */
function processVariant(variant) {
  const variantDir = path.join(iconsDir, variant.dir);
  const cssRules = [];
  
  if (!fs.existsSync(variantDir)) {
    console.warn(`⚠️  目录不存在: ${variantDir}`);
    return cssRules;
  }
  
  const files = fs.readdirSync(variantDir);
  
  files.forEach(file => {
    if (file.endsWith('.svg')) {
      const iconName = path.basename(file, '.svg') + variant.suffix;
      const filePath = path.join(variantDir, file);
      
      try {
        const svgContent = fs.readFileSync(filePath, 'utf8');
        const cssRule = generateIconCSS(iconName, svgContent, variant.size);
        cssRules.push(cssRule);
      } catch (error) {
        console.error(`❌ 处理图标失败 ${iconName}:`, error.message);
      }
    }
  });
  
  return cssRules;
}

/**
 * 生成完整的 CSS 文件
 */
function generateCSS() {
  console.log('🎨 开始生成 Heroicons CSS 定义...');
  
  let cssContent = `/*
 * 自动生成的 Heroicons CSS 定义
 * 包含所有图标的完整 CSS 类定义
 * 生成时间: ${new Date().toISOString()}
 */

`;

  let totalIcons = 0;
  
  // 处理每个变体
  variants.forEach(variant => {
    console.log(`📂 处理变体: ${variant.suffix || 'outline'}`);
    const cssRules = processVariant(variant);
    
    if (cssRules.length > 0) {
      cssContent += `/* ${variant.suffix || 'Outline'} 图标 (${variant.dir}) */\n`;
      cssContent += cssRules.join('\n\n') + '\n\n';
      totalIcons += cssRules.length;
      console.log(`   ✅ 生成了 ${cssRules.length} 个图标`);
    }
  });
  
  // 写入文件
  try {
    fs.writeFileSync(outputFile, cssContent, 'utf8');
    console.log(`🎉 成功生成 CSS 文件: ${outputFile}`);
    console.log(`📊 总计生成 ${totalIcons} 个图标类`);
  } catch (error) {
    console.error('❌ 写入文件失败:', error.message);
    process.exit(1);
  }
}

// 执行生成
if (require.main === module) {
  generateCSS();
}

module.exports = { generateCSS };
